import os
import whisper
import tempfile
import logging
from flask import Blueprint, request, jsonify
from flask_cors import cross_origin

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

transcription_bp = Blueprint('transcription', __name__)

# Load tiny Whisper model for low resource usage
try:
    logger.info("Loading Whisper tiny model (low resource usage)...")
    model = whisper.load_model("tiny")  # Uses <1GB RAM
    logger.info("Whisper tiny model loaded successfully")
except Exception as e:
    logger.error(f"Failed to load Whisper model: {e}")
    model = None

@transcription_bp.route('/transcribe', methods=['POST'])
@cross_origin()
def transcribe_video():
    try:
        if 'video' not in request.files:
            return jsonify({'error': 'No video file provided'}), 400
        
        file = request.files['video']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.mp4') as tmpfile:
            file.save(tmpfile.name)
            temp_filename = tmpfile.name

        logger.info(f"Processing file: {file.filename}")
        
        if not model:
            return jsonify({'error': 'Transcription model not available'}), 500

        # Transcribe with English language specification
        result = model.transcribe(
            temp_filename, 
            language='en',
            word_timestamps=True
        )
        
        # Extract detailed word-level data with enhanced formatting
        words_data = []
        segments_data = []
        
        for segment in result.get('segments', []):
            segment_words = []
            for word_info in segment.get('words', []):
                word_data = {
                    'word': word_info['word'].strip(),
                    'start': round(word_info['start'], 3),
                    'end': round(word_info['end'], 3),
                    'duration': round(word_info['end'] - word_info['start'], 3)
                }
                words_data.append(word_data)
                segment_words.append(word_data)
            
            # Add segment data for phrase-level timing
            segments_data.append({
                'text': segment['text'].strip(),
                'start': round(segment['start'], 3),
                'end': round(segment['end'], 3),
                'words': segment_words
            })
        
        transcription = result['text']
        
        # Clean up temporary file
        os.unlink(temp_filename)
        
        logger.info("Transcription completed successfully")
        return jsonify({
            'transcription': transcription,
            'words': words_data,
            'segments': segments_data,
            'metadata': {
                'language': result.get('language', 'en'),
                'language_probability': result.get('language_probs', {})
            }
        })
        
    except Exception as e:
        # Clean up temporary files
        if 'temp_filename' in locals() and os.path.exists(temp_filename):
            os.unlink(temp_filename)
        
        logger.error(f"Transcription error: {str(e)}")
        return jsonify({'error': f'Transcription failed: {str(e)}'}), 500

@transcription_bp.route('/health', methods=['GET'])
@cross_origin()
def health_check():
    """Health check endpoint to verify transcription service status"""
    return jsonify({
        'status': 'healthy',
        'whisper_model_loaded': model is not None,
        'model_type': 'tiny' if model else None
    })

