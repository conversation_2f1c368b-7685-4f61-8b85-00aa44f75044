<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Caption Studio - Professional Video Caption Editor</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">Caption Studio</h1>
                <p class="app-subtitle">Professional video caption editing with AI transcription</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" id="exportBtn" disabled>Export Video</button>
                <button class="btn btn-primary" id="saveProjectBtn" disabled>Save Project</button>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Upload Section (shown initially) -->
            <div class="upload-section" id="uploadSection">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">🎬</div>
                    <h2>Upload Your Video</h2>
                    <p>Drag & drop your video file or click to browse</p>
                    <button class="btn btn-primary" id="browseBtn">Choose Video File</button>
                    <input type="file" id="videoInput" accept="video/*" style="display: none;">
                    <div class="upload-info">
                        <p>Supported formats: MP4, MOV, AVI, MKV</p>
                        <p>Maximum file size: 100MB</p>
                    </div>
                </div>
                <div class="file-info" id="fileInfo"></div>
                <div class="progress-container" id="progressContainer" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">Processing...</div>
                </div>
            </div>

            <!-- Editor Section (shown after upload) -->
            <div class="editor-section" id="editorSection" style="display: none;">
                <!-- Video Player Area -->
                <div class="video-area">
                    <div class="video-container">
                        <video id="videoPlayer" controls>
                            Your browser does not support the video tag.
                        </video>
                        <div class="caption-overlay" id="captionOverlay"></div>
                    </div>
                    
                    <!-- Video Controls -->
                    <div class="video-controls">
                        <button class="control-btn" id="playPauseBtn">⏯️</button>
                        <div class="time-display">
                            <span id="currentTime">00:00</span> / <span id="totalTime">00:00</span>
                        </div>
                        <input type="range" class="seek-bar" id="seekBar" min="0" max="100" value="0">
                        <div class="volume-control">
                            <button class="control-btn" id="muteBtn">🔊</button>
                            <input type="range" class="volume-bar" id="volumeBar" min="0" max="100" value="100">
                        </div>
                    </div>
                </div>

                <!-- Timeline Editor -->
                <div class="timeline-section">
                    <div class="timeline-header">
                        <h3>Caption Timeline</h3>
                        <div class="timeline-controls">
                            <div class="timeline-zoom-controls">
                                <span style="font-size: 0.8rem; color: #a0a0c0;">Zoom:</span>
                                <input type="range" class="zoom-slider" id="zoomSlider" min="0.5" max="5" step="0.1" value="1">
                            </div>
                            <button class="btn btn-small" id="zoomInBtn">🔍+</button>
                            <button class="btn btn-small" id="zoomOutBtn">🔍-</button>
                            <button class="btn btn-small" id="autoSyncBtn">Auto Sync</button>
                            <button class="btn btn-small" id="addCaptionBtn">+ Caption</button>
                        </div>
                    </div>
                    
                    <!-- Waveform Visualization -->
                    <div class="waveform-container" id="waveformContainer" style="display: none;">
                        <div class="waveform-bars" id="waveformBars">
                            <!-- Waveform bars will be generated here -->
                        </div>
                    </div>
                    
                    <div class="timeline-container" id="timelineContainer">
                        <div class="timeline-ruler" id="timelineRuler"></div>
                        <div class="timeline-track" id="timelineTrack">
                            <div class="playhead" id="playhead"></div>
                        </div>
                    </div>
                    
                    <!-- Timeline Status -->
                    <div style="margin-top: 10px; display: flex; justify-content: between; align-items: center; font-size: 0.8rem; color: #a0a0c0;">
                        <span id="timelineStatus">No captions loaded</span>
                        <div style="margin-left: auto;">
                            <span id="selectedWordInfo"></span>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Controls -->
                <div class="sidebar">
                    <!-- Caption Styling -->
                    <div class="control-panel">
                        <h3>Caption Style</h3>
                        <div class="control-group">
                            <label for="fontFamily">Font Family</label>
                            <select id="fontFamily" class="form-control">
                                <option value="Arial">Arial</option>
                                <option value="Helvetica">Helvetica</option>
                                <option value="Georgia">Georgia</option>
                                <option value="Times New Roman">Times New Roman</option>
                                <option value="Courier New">Courier New</option>
                                <option value="Impact">Impact</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label for="fontSize">Font Size: <span id="fontSizeValue">24px</span></label>
                            <input type="range" id="fontSize" class="form-range" min="12" max="72" value="24">
                        </div>
                        <div class="control-group">
                            <label for="fontColor">Text Color</label>
                            <input type="color" id="fontColor" class="form-control" value="#ffffff">
                        </div>
                        <div class="control-group">
                            <label for="backgroundColor">Background Color</label>
                            <input type="color" id="backgroundColor" class="form-control" value="#000000">
                        </div>
                        <div class="control-group">
                            <label>
                                <input type="checkbox" id="showBackground" checked> Show Background
                            </label>
                        </div>
                    </div>

                    <!-- Position Controls -->
                    <div class="control-panel">
                        <h3>Position & Size</h3>
                        <div class="control-group">
                            <label for="positionX">Horizontal: <span id="positionXValue">50%</span></label>
                            <input type="range" id="positionX" class="form-range" min="0" max="100" value="50">
                        </div>
                        <div class="control-group">
                            <label for="positionY">Vertical: <span id="positionYValue">85%</span></label>
                            <input type="range" id="positionY" class="form-range" min="0" max="100" value="85">
                        </div>
                        <div class="control-group">
                            <label for="captionWidth">Width: <span id="captionWidthValue">80%</span></label>
                            <input type="range" id="captionWidth" class="form-range" min="20" max="100" value="80">
                        </div>
                    </div>

                    <!-- Video Effects -->
                    <div class="control-panel">
                        <h3>Video Effects</h3>
                        <div class="control-group">
                            <label for="videoEffect">Effect Type</label>
                            <select id="videoEffect" class="form-control">
                                <option value="none">None</option>
                                <option value="grayscale">Grayscale</option>
                                <option value="sepia">Sepia</option>
                                <option value="blur">Blur</option>
                                <option value="brightness">Brightness</option>
                                <option value="contrast">Contrast</option>
                                <option value="saturate">Saturate</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label for="effectIntensity">Intensity: <span id="effectIntensityValue">100%</span></label>
                            <input type="range" id="effectIntensity" class="form-range" min="0" max="200" value="100">
                        </div>
                    </div>

                    <!-- Transcript Panel -->
                    <div class="control-panel">
                        <h3>Transcript</h3>
                        <div class="transcript-container" id="transcriptContainer">
                            <div class="transcript-loading" id="transcriptLoading">
                                <div class="spinner"></div>
                                <p>Generating transcript...</p>
                            </div>
                            <div class="transcript-content" id="transcriptContent" style="display: none;">
                                <!-- Transcript words will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Status Bar -->
        <div class="status-bar" id="statusBar">
            <span id="statusText">Ready to upload video</span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/timeline.js"></script>
    <script src="js/video.js"></script>
    <script src="js/effects.js"></script>
</body>
</html>

