// Video Effects and Filters
class VideoEffects {
    constructor(captionStudio) {
        this.app = captionStudio;
        this.currentFilters = [];
        this.presets = {
            cinematic: {
                contrast: 110,
                brightness: 95,
                saturate: 120,
                sepia: 10
            },
            vintage: {
                sepia: 60,
                contrast: 90,
                brightness: 110,
                saturate: 80
            },
            dramatic: {
                contrast: 140,
                brightness: 85,
                saturate: 150,
                grayscale: 0
            },
            soft: {
                blur: 1,
                brightness: 105,
                contrast: 95,
                saturate: 90
            }
        };
        
        this.setupEffectControls();
    }

    setupEffectControls() {
        this.addEffectPresets();
        this.addAdvancedFilters();
        this.setupRealTimePreview();
    }

    addEffectPresets() {
        const effectPanelContent = document.getElementById('videoEffectsPanel');
        if (!effectPanelContent) return;

        const presetsGroup = document.createElement('div');
        presetsGroup.className = 'control-group';
        presetsGroup.innerHTML = `
            <label>Effect Presets</label>
            <div class="preset-buttons" style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-top: 8px;">
                <button class="btn btn-small preset-btn" data-preset="none">None</button>
                <button class="btn btn-small preset-btn" data-preset="cinematic">Cinematic</button>
                <button class="btn btn-small preset-btn" data-preset="vintage">Vintage</button>
                <button class="btn btn-small preset-btn" data-preset="dramatic">Dramatic</button>
                <button class="btn btn-small preset-btn" data-preset="soft">Soft</button>
                <button class="btn btn-small preset-btn" data-preset="custom">Custom</button>
            </div>
        `;

        // Insert after the effect type selector
        const effectSelector = document.getElementById('videoEffect');
        effectSelector.parentElement.insertAdjacentElement('afterend', presetsGroup);

        // Add event listeners
        presetsGroup.querySelectorAll('.preset-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.applyPreset(btn.dataset.preset);
                this.updateActivePresetButton(btn);
            });
        });
    }

    addAdvancedFilters() {
        const effectPanelContent = document.getElementById('videoEffectsPanel');
        if (!effectPanelContent) return;

        const advancedGroup = document.createElement('div');
        advancedGroup.className = 'control-group advanced-filters';
        advancedGroup.innerHTML = `
            <label>Advanced Filters</label>
            <div class="filter-controls">
                <div class="filter-control">
                    <label for="brightnessFilter">Brightness: <span id="brightnessValue">100%</span></label>
                    <input type="range" id="brightnessFilter" class="form-range" min="0" max="200" value="100">
                </div>
                <div class="filter-control">
                    <label for="contrastFilter">Contrast: <span id="contrastValue">100%</span></label>
                    <input type="range" id="contrastFilter" class="form-range" min="0" max="200" value="100">
                </div>
                <div class="filter-control">
                    <label for="saturateFilter">Saturation: <span id="saturateValue">100%</span></label>
                    <input type="range" id="saturateFilter" class="form-range" min="0" max="200" value="100">
                </div>
                <div class="filter-control">
                    <label for="hueFilter">Hue: <span id="hueValue">0°</span></label>
                    <input type="range" id="hueFilter" class="form-range" min="0" max="360" value="0">
                </div>
                <div class="filter-control">
                    <label for="blurFilter">Blur: <span id="blurValue">0px</span></label>
                    <input type="range" id="blurFilter" class="form-range" min="0" max="10" value="0" step="0.1">
                </div>
                <div class="filter-control">
                    <label for="sepiaFilter">Sepia: <span id="sepiaValue">0%</span></label>
                    <input type="range" id="sepiaFilter" class="form-range" min="0" max="100" value="0">
                </div>
                <div class="filter-control">
                    <label for="grayscaleFilter">Grayscale: <span id="grayscaleValue">0%</span></label>
                    <input type="range" id="grayscaleFilter" class="form-range" min="0" max="100" value="0">
                </div>
                <div class="filter-control">
                    <label for="invertFilter">Invert: <span id="invertValue">0%</span></label>
                    <input type="range" id="invertFilter" class="form-range" min="0" max="100" value="0">
                </div>
            </div>
            <div style="margin-top: 15px; display: flex; gap: 10px;">
                <button class="btn btn-small" id="resetFiltersBtn">Reset All</button>
                <button class="btn btn-small" id="savePresetBtn">Save Preset</button>
            </div>
        `;

        effectPanelContent.appendChild(advancedGroup);

        // Add event listeners for all filter controls
        this.setupFilterListeners();
    }

    setupFilterListeners() {
        const filterControls = [
            'brightness', 'contrast', 'saturate', 'hue', 
            'blur', 'sepia', 'grayscale', 'invert'
        ];

        filterControls.forEach(filter => {
            const slider = document.getElementById(filter + 'Filter');
            const valueDisplay = document.getElementById(filter + 'Value');
            
            if (slider && valueDisplay) {
                slider.addEventListener('input', (e) => {
                    const value = e.target.value;
                    const unit = this.getFilterUnit(filter);
                    valueDisplay.textContent = value + unit;
                    this.updateVideoFilters();
                });
            }
        });

        // Reset button
        const resetBtn = document.getElementById('resetFiltersBtn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetAllFilters());
        }

        // Save preset button
        const saveBtn = document.getElementById('savePresetBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveCustomPreset());
        }
    }

    getFilterUnit(filterName) {
        switch (filterName) {
            case 'hue': return '°';
            case 'blur': return 'px';
            default: return '%';
        }
    }

    updateVideoFilters() {
        const filters = [];
        
        // Get all filter values
        const brightness = document.getElementById('brightnessFilter')?.value || 100;
        const contrast = document.getElementById('contrastFilter')?.value || 100;
        const saturate = document.getElementById('saturateFilter')?.value || 100;
        const hue = document.getElementById('hueFilter')?.value || 0;
        const blur = document.getElementById('blurFilter')?.value || 0;
        const sepia = document.getElementById('sepiaFilter')?.value || 0;
        const grayscale = document.getElementById('grayscaleFilter')?.value || 0;
        const invert = document.getElementById('invertFilter')?.value || 0;

        // Build filter string
        if (brightness != 100) filters.push(`brightness(${brightness}%)`);
        if (contrast != 100) filters.push(`contrast(${contrast}%)`);
        if (saturate != 100) filters.push(`saturate(${saturate}%)`);
        if (hue != 0) filters.push(`hue-rotate(${hue}deg)`);
        if (blur != 0) filters.push(`blur(${blur}px)`);
        if (sepia != 0) filters.push(`sepia(${sepia}%)`);
        if (grayscale != 0) filters.push(`grayscale(${grayscale}%)`);
        if (invert != 0) filters.push(`invert(${invert}%)`);

        // Apply filters to video
        const filterString = filters.length > 0 ? filters.join(' ') : 'none';
        this.app.videoPlayer.style.filter = filterString;
        this.currentFilters = filters;

        // Update active preset button
        this.updateActivePresetButton(null);
    }

    applyPreset(presetName) {
        if (presetName === 'none') {
            this.resetAllFilters();
            return;
        }

        if (presetName === 'custom') {
            // Load custom preset from localStorage
            const customPreset = localStorage.getItem('captionStudioCustomPreset');
            if (customPreset) {
                const preset = JSON.parse(customPreset);
                this.applyFilterValues(preset);
            }
            return;
        }

        const preset = this.presets[presetName];
        if (preset) {
            this.applyFilterValues(preset);
        }
    }

    applyFilterValues(values) {
        // Reset all filters first
        this.resetAllFilters();

        // Apply preset values
        Object.entries(values).forEach(([filter, value]) => {
            const slider = document.getElementById(filter + 'Filter');
            const valueDisplay = document.getElementById(filter + 'Value');
            
            if (slider && valueDisplay) {
                slider.value = value;
                const unit = this.getFilterUnit(filter);
                valueDisplay.textContent = value + unit;
            }
        });

        // Update video filters
        this.updateVideoFilters();
    }

    resetAllFilters() {
        const defaultValues = {
            brightness: 100,
            contrast: 100,
            saturate: 100,
            hue: 0,
            blur: 0,
            sepia: 0,
            grayscale: 0,
            invert: 0
        };

        this.applyFilterValues(defaultValues);
    }

    updateActivePresetButton(activeBtn) {
        // Remove active class from all preset buttons
        document.querySelectorAll('.preset-btn').forEach(btn => {
            btn.classList.remove('active');
            btn.style.background = '';
        });

        // Add active class to selected button
        if (activeBtn) {
            activeBtn.classList.add('active');
            activeBtn.style.background = 'linear-gradient(45deg, #4db8ff, #3a9fe0)';
            activeBtn.style.color = '#0c0c14';
        }
    }

    saveCustomPreset() {
        const customPreset = {
            brightness: document.getElementById('brightnessFilter')?.value || 100,
            contrast: document.getElementById('contrastFilter')?.value || 100,
            saturate: document.getElementById('saturateFilter')?.value || 100,
            hue: document.getElementById('hueFilter')?.value || 0,
            blur: document.getElementById('blurFilter')?.value || 0,
            sepia: document.getElementById('sepiaFilter')?.value || 0,
            grayscale: document.getElementById('grayscaleFilter')?.value || 0,
            invert: document.getElementById('invertFilter')?.value || 0
        };

        localStorage.setItem('captionStudioCustomPreset', JSON.stringify(customPreset));
        this.app.updateStatus('Custom preset saved');
    }

    setupRealTimePreview() {
        // Add preview toggle
        const effectPanel = document.querySelector('.control-panel:has(#videoEffect)');
        if (!effectPanel) return;

        const previewToggle = document.createElement('div');
        previewToggle.className = 'control-group';
        previewToggle.innerHTML = `
            <label>
                <input type="checkbox" id="realTimePreview" checked> Real-time Preview
            </label>
            <div style="margin-top: 10px;">
                <button class="btn btn-small" id="beforeAfterBtn">Before/After</button>
            </div>
        `;

        effectPanel.appendChild(previewToggle);

        // Setup preview toggle
        const previewCheckbox = document.getElementById('realTimePreview');
        previewCheckbox.addEventListener('change', (e) => {
            if (!e.target.checked) {
                this.app.videoPlayer.style.filter = 'none';
            } else {
                this.updateVideoFilters();
            }
        });

        // Setup before/after comparison
        const beforeAfterBtn = document.getElementById('beforeAfterBtn');
        beforeAfterBtn.addEventListener('mousedown', () => {
            this.app.videoPlayer.style.filter = 'none';
        });

        beforeAfterBtn.addEventListener('mouseup', () => {
            if (previewCheckbox.checked) {
                this.updateVideoFilters();
            }
        });

        beforeAfterBtn.addEventListener('mouseleave', () => {
            if (previewCheckbox.checked) {
                this.updateVideoFilters();
            }
        });
    }

    // Export current filter settings
    getFilterSettings() {
        return {
            brightness: document.getElementById('brightnessFilter')?.value || 100,
            contrast: document.getElementById('contrastFilter')?.value || 100,
            saturate: document.getElementById('saturateFilter')?.value || 100,
            hue: document.getElementById('hueFilter')?.value || 0,
            blur: document.getElementById('blurFilter')?.value || 0,
            sepia: document.getElementById('sepiaFilter')?.value || 0,
            grayscale: document.getElementById('grayscaleFilter')?.value || 0,
            invert: document.getElementById('invertFilter')?.value || 0
        };
    }

    // Load filter settings
    loadFilterSettings(settings) {
        if (settings) {
            this.applyFilterValues(settings);
        }
    }

    // Advanced color grading
    addColorGrading() {
        const effectPanel = document.querySelector('.control-panel:has(#videoEffect)');
        if (!effectPanel) return;

        const colorGradingGroup = document.createElement('div');
        colorGradingGroup.className = 'control-group color-grading';
        colorGradingGroup.innerHTML = `
            <label>Color Grading</label>
            <div class="color-grading-controls">
                <div class="color-control">
                    <label for="shadowsColor">Shadows</label>
                    <input type="color" id="shadowsColor" class="form-control" value="#000000">
                    <input type="range" id="shadowsIntensity" class="form-range" min="0" max="100" value="0">
                </div>
                <div class="color-control">
                    <label for="midtonesColor">Midtones</label>
                    <input type="color" id="midtonesColor" class="form-control" value="#808080">
                    <input type="range" id="midtonesIntensity" class="form-range" min="0" max="100" value="0">
                </div>
                <div class="color-control">
                    <label for="highlightsColor">Highlights</label>
                    <input type="color" id="highlightsColor" class="form-control" value="#ffffff">
                    <input type="range" id="highlightsIntensity" class="form-range" min="0" max="100" value="0">
                </div>
            </div>
        `;

        effectPanel.appendChild(colorGradingGroup);

        // Note: Advanced color grading would require WebGL shaders for full implementation
        // This is a placeholder for the UI structure
    }

    // Transition effects between filter changes
    addTransitionEffect(duration = 300) {
        this.app.videoPlayer.style.transition = `filter ${duration}ms ease-in-out`;
        
        setTimeout(() => {
            this.app.videoPlayer.style.transition = '';
        }, duration);
    }

    // Keyframe-based effects (for future implementation)
    createKeyframeEffect(keyframes) {
        // This would allow effects to change over time
        // Implementation would require timeline integration
        console.log('Keyframe effects not yet implemented:', keyframes);
    }
}

// Add CSS for effect controls
const effectsStyle = document.createElement('style');
effectsStyle.textContent = `
    .advanced-filters {
        border-top: 1px solid #2a2a4a;
        margin-top: 20px;
        padding-top: 20px;
    }
    
    .filter-controls {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    
    .filter-control {
        display: flex;
        flex-direction: column;
        gap: 6px;
    }
    
    .filter-control label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.85rem;
    }
    
    .preset-buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
        margin-top: 8px;
    }
    
    .preset-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
        background: #2a2a3a;
        color: #e6e6f0;
        border: 1px solid #3a3a5a;
        transition: all 0.2s ease;
    }
    
    .preset-btn:hover {
        background: #3a3a4a;
        border-color: #4db8ff;
    }
    
    .preset-btn.active {
        background: linear-gradient(45deg, #4db8ff, #3a9fe0) !important;
        color: #0c0c14 !important;
        border-color: #4db8ff !important;
    }
    
    .color-grading-controls {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    
    .color-control {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    
    .color-control label {
        font-size: 0.85rem;
        color: #c0c0e0;
    }
    
    .color-control input[type="color"] {
        width: 100%;
        height: 30px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
`;
document.head.appendChild(effectsStyle);

// Initialize effects when main app is ready
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        if (window.captionStudio) {
            window.videoEffects = new VideoEffects(window.captionStudio);
        }
    }, 100);
});

