// Timeline Editor Functionality
class TimelineEditor {
    constructor(captionStudio) {
        this.app = captionStudio;
        this.isDragging = false;
        this.dragStartX = 0;
        this.dragStartTime = 0;
        this.snapThreshold = 5; // pixels
        this.minWordDuration = 0.1; // seconds
        
        this.setupTimelineInteractions();
    }

    setupTimelineInteractions() {
        // Timeline click to seek
        this.app.timelineTrack.addEventListener('click', (e) => this.handleTimelineClick(e));
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
        
        // Resize observer for timeline responsiveness
        if (window.ResizeObserver) {
            const resizeObserver = new ResizeObserver(() => {
                if (this.app.transcriptionData) {
                    this.app.initializeTimeline();
                    this.optimizeWordBlockPositions();
                }
            });
            resizeObserver.observe(this.app.timelineContainer);
        }
    }

    optimizeWordBlockPositions() {
        const wordBlocks = Array.from(this.app.timelineTrack.getElementsByClassName('word-block'));
        const rows = [[]];
        
        wordBlocks.forEach((block) => {
            const rect = block.getBoundingClientRect();
            let placed = false;
            
            for (let row of rows) {
                if (row.length === 0 || !this.overlapsWithRow(block, row)) {
                    row.push(block);
                    placed = true;
                    break;
                }
            }
            
            if (!placed) {
                rows.push([block]);
                block.style.top = `${(rows.length - 1) * 28}px`; // 28px spacing between rows
            } else {
                block.style.top = `${(rows.length - 1) * 28}px`;
            }
        });
        
        // Update timeline track height
        this.app.timelineTrack.style.height = `${rows.length * 28 + 10}px`;
    }

    overlapsWithRow(block, row) {
        const blockRect = block.getBoundingClientRect();
        return row.some(existingBlock => {
            const existingRect = existingBlock.getBoundingClientRect();
            return !(blockRect.right < existingRect.left || blockRect.left > existingRect.right);
        });
    }
    }

    handleTimelineClick(event) {
        // Don't seek if clicking on a word block
        if (event.target.classList.contains('word-block')) {
            return;
        }

        const rect = this.app.timelineTrack.getBoundingClientRect();
        const clickX = event.clientX - rect.left - 10; // Account for padding
        const trackWidth = this.app.timelineTrack.offsetWidth - 20;
        const duration = this.app.videoPlayer.duration;
        
        if (duration && trackWidth > 0) {
            const clickTime = (clickX / trackWidth) * duration;
            this.app.videoPlayer.currentTime = Math.max(0, Math.min(clickTime, duration));
        }
    }

    handleKeyboardShortcuts(event) {
        // Only handle shortcuts when not typing in input fields
        if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
            return;
        }

        switch (event.code) {
            case 'Space':
                event.preventDefault();
                this.app.togglePlayPause();
                break;
            case 'ArrowLeft':
                event.preventDefault();
                this.seekRelative(-1);
                break;
            case 'ArrowRight':
                event.preventDefault();
                this.seekRelative(1);
                break;
            case 'ArrowUp':
                event.preventDefault();
                this.seekRelative(-10);
                break;
            case 'ArrowDown':
                event.preventDefault();
                this.seekRelative(10);
                break;
            case 'Delete':
            case 'Backspace':
                if (this.app.selectedWordBlock) {
                    event.preventDefault();
                    this.deleteSelectedWord();
                }
                break;
            case 'KeyS':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    this.app.saveProject();
                }
                break;
            case 'KeyZ':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    this.undo();
                }
                break;
        }
    }

    seekRelative(seconds) {
        const newTime = this.app.videoPlayer.currentTime + seconds;
        this.app.videoPlayer.currentTime = Math.max(0, Math.min(newTime, this.app.videoPlayer.duration));
    }

    deleteSelectedWord() {
        if (!this.app.selectedWordBlock) return;

        const index = parseInt(this.app.selectedWordBlock.dataset.index);
        
        // Remove from transcription data
        if (this.app.transcriptionData && this.app.transcriptionData.words) {
            this.app.transcriptionData.words.splice(index, 1);
        }

        // Remove from timeline
        this.app.selectedWordBlock.remove();
        this.app.selectedWordBlock = null;

        // Rebuild timeline to update indices
        this.app.initializeTimeline();
        this.app.updateStatus('Word deleted');
    }

    // Enhanced word block creation with better positioning
    createAdvancedWordBlock(word, index, duration, trackWidth) {
        const block = document.createElement('div');
        block.className = 'word-block';
        block.textContent = word.word;
        block.dataset.index = index;
        block.dataset.start = word.start;
        block.dataset.end = word.end;
        block.dataset.originalStart = word.start;
        block.dataset.originalEnd = word.end;

        // Calculate position and width with better spacing
        const left = (word.start / duration) * trackWidth;
        const wordDuration = word.end - word.start;
        const minWidth = Math.max(word.word.length * 8, 50); // Dynamic minimum width
        const calculatedWidth = (wordDuration / duration) * trackWidth;
        const width = Math.max(calculatedWidth, minWidth);

        block.style.left = left + 'px';
        block.style.width = width + 'px';
        
        // Improved row calculation to prevent overlap
        const row = this.calculateOptimalRow(word, index, duration, trackWidth);
        block.style.top = (15 + row * 45) + 'px'; // Increased spacing between rows

        // Add visual indicators for timing accuracy
        this.addTimingIndicators(block, word);

        // Enhanced event listeners
        this.addAdvancedWordBlockListeners(block);

        return block;
    }

    calculateOptimalRow(word, index, duration, trackWidth) {
        // More sophisticated row calculation to minimize overlaps
        const existingBlocks = this.app.timelineTrack.querySelectorAll('.word-block');
        const wordLeft = (word.start / duration) * trackWidth;
        const wordRight = (word.end / duration) * trackWidth;
        
        // Check each row for conflicts
        for (let row = 0; row < 4; row++) {
            let hasConflict = false;
            
            for (let block of existingBlocks) {
                const blockRow = Math.floor((parseInt(block.style.top) - 15) / 45);
                if (blockRow === row) {
                    const blockLeft = parseInt(block.style.left);
                    const blockRight = blockLeft + parseInt(block.style.width);
                    
                    // Check for overlap with 10px buffer
                    if (!(wordRight + 10 < blockLeft || wordLeft > blockRight + 10)) {
                        hasConflict = true;
                        break;
                    }
                }
            }
            
            if (!hasConflict) {
                return row;
            }
        }
        
        // If all rows have conflicts, use modulo as fallback
        return index % 4;
    }

    addTimingIndicators(block, word) {
        // Add confidence indicator based on word duration
        const duration = word.end - word.start;
        let confidence = 'high';
        
        if (duration < 0.1) confidence = 'low';
        else if (duration < 0.3) confidence = 'medium';
        
        block.classList.add(`confidence-${confidence}`);
        
        // Add tooltip with timing info
        block.title = `${word.word}\nStart: ${word.start.toFixed(2)}s\nEnd: ${word.end.toFixed(2)}s\nDuration: ${duration.toFixed(2)}s`;
    }

    addAdvancedWordBlockListeners(block) {
        // Click to select
        block.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectWordBlock(block);
        });

        // Double-click to edit text
        block.addEventListener('dblclick', (e) => {
            e.stopPropagation();
            this.editWordText(block);
        });

        // Right-click context menu
        block.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showContextMenu(e, block);
        });

        // Drag functionality
        block.addEventListener('mousedown', (e) => {
            if (e.button === 0) { // Left mouse button
                this.startAdvancedDrag(e, block);
            }
        });

        // Hover effects
        block.addEventListener('mouseenter', () => {
            if (!block.classList.contains('selected')) {
                block.style.transform = 'translateY(-2px)';
                block.style.boxShadow = '0 4px 12px rgba(77, 184, 255, 0.3)';
            }
        });

        block.addEventListener('mouseleave', () => {
            if (!block.classList.contains('selected')) {
                block.style.transform = '';
                block.style.boxShadow = '';
            }
        });
    }

    selectWordBlock(block) {
        // Remove previous selection
        if (this.app.selectedWordBlock) {
            this.app.selectedWordBlock.classList.remove('selected');
            this.app.selectedWordBlock.style.transform = '';
            this.app.selectedWordBlock.style.boxShadow = '';
        }

        // Select new block
        this.app.selectedWordBlock = block;
        block.classList.add('selected');
        block.style.transform = 'translateY(-2px)';
        block.style.boxShadow = '0 0 0 2px #4db8ff, 0 4px 12px rgba(77, 184, 255, 0.4)';

        // Seek to word time
        const startTime = parseFloat(block.dataset.start);
        this.app.videoPlayer.currentTime = startTime;

        // Update status
        this.app.updateStatus(`Selected: "${block.textContent}" (${startTime.toFixed(2)}s - ${parseFloat(block.dataset.end).toFixed(2)}s)`);
    }

    editWordText(block) {
        const currentText = block.textContent;
        const input = document.createElement('input');
        input.type = 'text';
        input.value = currentText;
        input.className = 'word-edit-input';
        input.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #1a1a2a;
            color: #e6e6f0;
            border: 2px solid #4db8ff;
            border-radius: 4px;
            padding: 2px 6px;
            font-size: 0.8rem;
            font-weight: 600;
            z-index: 1001;
        `;

        block.style.position = 'relative';
        block.appendChild(input);
        input.focus();
        input.select();

        const finishEdit = () => {
            const newText = input.value.trim();
            if (newText && newText !== currentText) {
                block.textContent = newText;
                
                // Update transcription data
                const index = parseInt(block.dataset.index);
                if (this.app.transcriptionData && this.app.transcriptionData.words[index]) {
                    this.app.transcriptionData.words[index].word = newText;
                }
                
                this.app.updateStatus(`Word updated: "${newText}"`);
            } else {
                block.textContent = currentText;
            }
            
            input.remove();
        };

        input.addEventListener('blur', finishEdit);
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                finishEdit();
            } else if (e.key === 'Escape') {
                block.textContent = currentText;
                input.remove();
            }
        });
    }

    showContextMenu(event, block) {
        // Remove existing context menu
        const existingMenu = document.querySelector('.timeline-context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }

        const menu = document.createElement('div');
        menu.className = 'timeline-context-menu';
        menu.style.cssText = `
            position: fixed;
            top: ${event.clientY}px;
            left: ${event.clientX}px;
            background: #1a1a2a;
            border: 1px solid #3a3a5a;
            border-radius: 6px;
            padding: 8px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 1000;
            min-width: 150px;
        `;

        const menuItems = [
            { text: 'Edit Text', action: () => this.editWordText(block) },
            { text: 'Split Word', action: () => this.splitWord(block) },
            { text: 'Merge with Next', action: () => this.mergeWithNext(block) },
            { text: 'Reset Timing', action: () => this.resetWordTiming(block) },
            { text: 'Delete Word', action: () => this.deleteWord(block) }
        ];

        menuItems.forEach(item => {
            const menuItem = document.createElement('div');
            menuItem.textContent = item.text;
            menuItem.style.cssText = `
                padding: 8px 16px;
                cursor: pointer;
                color: #e6e6f0;
                font-size: 0.9rem;
                transition: background 0.2s ease;
            `;
            
            menuItem.addEventListener('mouseenter', () => {
                menuItem.style.background = '#2a2a3a';
            });
            
            menuItem.addEventListener('mouseleave', () => {
                menuItem.style.background = '';
            });
            
            menuItem.addEventListener('click', () => {
                item.action();
                menu.remove();
            });
            
            menu.appendChild(menuItem);
        });

        document.body.appendChild(menu);

        // Close menu when clicking outside
        const closeMenu = (e) => {
            if (!menu.contains(e.target)) {
                menu.remove();
                document.removeEventListener('click', closeMenu);
            }
        };
        
        setTimeout(() => {
            document.addEventListener('click', closeMenu);
        }, 100);
    }

    startAdvancedDrag(event, block) {
        event.preventDefault();
        
        this.selectWordBlock(block);
        
        const startX = event.clientX;
        const startLeft = parseInt(block.style.left);
        const trackWidth = this.app.timelineTrack.offsetWidth - 20;
        const duration = this.app.videoPlayer.duration;
        
        block.classList.add('dragging');
        this.isDragging = true;

        // Create ghost element for better visual feedback
        const ghost = block.cloneNode(true);
        ghost.style.opacity = '0.5';
        ghost.style.pointerEvents = 'none';
        ghost.style.zIndex = '999';
        this.app.timelineTrack.appendChild(ghost);

        const handleMouseMove = (e) => {
            const deltaX = e.clientX - startX;
            const newLeft = Math.max(0, Math.min(startLeft + deltaX, trackWidth - parseInt(block.style.width)));
            
            // Snap to grid or other words
            const snappedLeft = this.snapToGrid(newLeft, trackWidth, duration);
            
            block.style.left = snappedLeft + 'px';
            ghost.style.left = snappedLeft + 'px';
            
            // Update timing preview
            const newStartTime = (snappedLeft / trackWidth) * duration;
            const wordDuration = parseFloat(block.dataset.end) - parseFloat(block.dataset.start);
            const newEndTime = newStartTime + wordDuration;
            
            // Show timing preview
            this.showTimingPreview(block, newStartTime, newEndTime);
        };

        const handleMouseUp = () => {
            block.classList.remove('dragging');
            ghost.remove();
            this.isDragging = false;
            
            // Update word timing
            this.updateWordTiming(block, trackWidth, duration);
            this.hideTimingPreview();
            
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    }

    snapToGrid(position, trackWidth, duration) {
        // Snap to second boundaries
        const timePerPixel = duration / trackWidth;
        const currentTime = (position / trackWidth) * duration;
        const snappedTime = Math.round(currentTime * 10) / 10; // Snap to 0.1 second
        return (snappedTime / duration) * trackWidth;
    }

    showTimingPreview(block, startTime, endTime) {
        let preview = document.querySelector('.timing-preview');
        if (!preview) {
            preview = document.createElement('div');
            preview.className = 'timing-preview';
            preview.style.cssText = `
                position: fixed;
                background: #1a1a2a;
                color: #4db8ff;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 0.8rem;
                font-weight: 600;
                border: 1px solid #3a3a5a;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 1002;
                pointer-events: none;
            `;
            document.body.appendChild(preview);
        }

        preview.textContent = `${startTime.toFixed(2)}s - ${endTime.toFixed(2)}s`;
        
        const rect = block.getBoundingClientRect();
        preview.style.left = rect.left + 'px';
        preview.style.top = (rect.top - 40) + 'px';
    }

    hideTimingPreview() {
        const preview = document.querySelector('.timing-preview');
        if (preview) {
            preview.remove();
        }
    }

    updateWordTiming(block, trackWidth, duration) {
        const newLeft = parseInt(block.style.left);
        const newStartTime = (newLeft / trackWidth) * duration;
        const wordDuration = parseFloat(block.dataset.end) - parseFloat(block.dataset.start);
        const newEndTime = newStartTime + wordDuration;

        // Ensure minimum duration
        const adjustedEndTime = Math.max(newEndTime, newStartTime + this.minWordDuration);

        block.dataset.start = newStartTime.toFixed(3);
        block.dataset.end = adjustedEndTime.toFixed(3);

        // Update transcription data
        const index = parseInt(block.dataset.index);
        if (this.app.transcriptionData && this.app.transcriptionData.words[index]) {
            this.app.transcriptionData.words[index].start = newStartTime;
            this.app.transcriptionData.words[index].end = adjustedEndTime;
        }

        this.app.updateStatus(`Word timing updated: ${newStartTime.toFixed(2)}s - ${adjustedEndTime.toFixed(2)}s`);
    }

    // Additional timeline features
    splitWord(block) {
        const index = parseInt(block.dataset.index);
        const word = this.app.transcriptionData.words[index];
        const text = word.word;
        
        if (text.length < 2) {
            this.app.updateStatus('Word too short to split', 'error');
            return;
        }

        const midPoint = Math.floor(text.length / 2);
        const firstPart = text.substring(0, midPoint);
        const secondPart = text.substring(midPoint);
        
        const duration = word.end - word.start;
        const splitTime = word.start + (duration / 2);

        // Update first word
        word.word = firstPart;
        word.end = splitTime;

        // Create second word
        const newWord = {
            word: secondPart,
            start: splitTime,
            end: word.end,
            duration: duration / 2
        };

        // Insert into transcription data
        this.app.transcriptionData.words.splice(index + 1, 0, newWord);

        // Rebuild timeline
        this.app.initializeTimeline();
        this.app.updateStatus(`Word split: "${firstPart}" | "${secondPart}"`);
    }

    mergeWithNext(block) {
        const index = parseInt(block.dataset.index);
        const words = this.app.transcriptionData.words;
        
        if (index >= words.length - 1) {
            this.app.updateStatus('No next word to merge with', 'error');
            return;
        }

        const currentWord = words[index];
        const nextWord = words[index + 1];

        // Merge words
        currentWord.word = currentWord.word + ' ' + nextWord.word;
        currentWord.end = nextWord.end;
        currentWord.duration = currentWord.end - currentWord.start;

        // Remove next word
        words.splice(index + 1, 1);

        // Rebuild timeline
        this.app.initializeTimeline();
        this.app.updateStatus(`Words merged: "${currentWord.word}"`);
    }

    resetWordTiming(block) {
        const index = parseInt(block.dataset.index);
        const originalStart = parseFloat(block.dataset.originalStart);
        const originalEnd = parseFloat(block.dataset.originalEnd);

        // Reset to original timing
        block.dataset.start = originalStart;
        block.dataset.end = originalEnd;

        // Update transcription data
        if (this.app.transcriptionData && this.app.transcriptionData.words[index]) {
            this.app.transcriptionData.words[index].start = originalStart;
            this.app.transcriptionData.words[index].end = originalEnd;
        }

        // Reposition block
        const duration = this.app.videoPlayer.duration;
        const trackWidth = this.app.timelineTrack.offsetWidth - 20;
        const left = (originalStart / duration) * trackWidth;
        block.style.left = left + 'px';

        this.app.updateStatus('Word timing reset to original');
    }

    deleteWord(block) {
        const index = parseInt(block.dataset.index);
        const word = this.app.transcriptionData.words[index];
        
        // Remove from transcription data
        this.app.transcriptionData.words.splice(index, 1);

        // Remove from timeline
        block.remove();
        this.app.selectedWordBlock = null;

        // Rebuild timeline to update indices
        this.app.initializeTimeline();
        this.app.updateStatus(`Word deleted: "${word.word}"`);
    }

    // Undo functionality (basic implementation)
    undo() {
        // In a full implementation, this would maintain a history stack
        this.app.updateStatus('Undo functionality not yet implemented');
    }
}

// Initialize timeline editor when main app is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait for main app to initialize
    setTimeout(() => {
        if (window.captionStudio) {
            window.timelineEditor = new TimelineEditor(window.captionStudio);
        }
    }, 100);
});

