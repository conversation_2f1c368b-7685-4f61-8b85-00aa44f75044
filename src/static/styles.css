/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #0c0c14;
    color: #e6e6f0;
    overflow-x: hidden;
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border-bottom: 1px solid #2a2a4a;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.header-content {
    flex: 1;
}

.app-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #4db8ff;
    margin-bottom: 2px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.app-subtitle {
    font-size: 0.8rem;
    color: #a0a0c0;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    gap: 15px;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(45deg, #4db8ff, #3a9fe0);
    color: #0c0c14;
    box-shadow: 0 4px 15px rgba(77, 184, 255, 0.3);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(77, 184, 255, 0.4);
}

.btn-secondary {
    background: linear-gradient(45deg, #2196F3, #1976D2);
    color: white;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.btn-secondary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9rem;
}

.btn:disabled {
    background: #3a3a5a;
    color: #8a8a9a;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 30px;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Upload Section */
.upload-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
}

.upload-area {
    background: #121220;
    border: 2px dashed #3a3a5a;
    border-radius: 15px;
    padding: 60px 40px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    max-width: 600px;
    width: 100%;
}

.upload-area:hover {
    border-color: #4db8ff;
    background: rgba(77, 184, 255, 0.05);
}

.upload-area.dragover {
    border-color: #4db8ff;
    background: rgba(77, 184, 255, 0.1);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.upload-area h2 {
    font-size: 1.8rem;
    margin-bottom: 10px;
    color: #4db8ff;
}

.upload-area p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    color: #a0a0c0;
}

.upload-info {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #2a2a4a;
}

.upload-info p {
    font-size: 0.9rem;
    margin-bottom: 5px;
    color: #8a8a9a;
}

.file-info {
    margin-top: 20px;
    padding: 15px;
    background: #1a1a2a;
    border-radius: 8px;
    font-size: 1rem;
    color: #e6e6f0;
}

/* Progress */
.progress-container {
    margin-top: 30px;
    width: 100%;
    max-width: 500px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #2a2a3a;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4db8ff, #3a9fe0);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    font-size: 0.9rem;
    color: #a0a0c0;
}

/* Editor Section */
.editor-section {
    display: grid;
    grid-template-columns: 1fr 300px 350px;
    grid-template-rows: auto 1fr;
    gap: 30px;
    height: calc(100vh - 200px);
}

.video-area {
    grid-column: 1;
    grid-row: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.caption-controls-area {
    grid-column: 2;
    grid-row: 1;
    display: flex;
    flex-direction: column;
}

.timeline-section {
    grid-column: 1 / 3;
    grid-row: 2;
    background: #121220;
    border-radius: 15px;
    padding: 20px;
    border: 1px solid #2a2a4a;
}

.sidebar {
    grid-column: 3;
    grid-row: 1 / -1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

/* Video Container */
.video-container {
    position: relative;
    background: #000;
    border-radius: 12px;
    overflow: hidden;
    aspect-ratio: 16/9;
    border: 1px solid #3a3a5a;
    max-width: 600px;
    width: 100%;
}

#videoPlayer {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.caption-overlay {
    position: absolute;
    bottom: 10%;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 24px;
    font-family: Arial, sans-serif;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    padding: 10px 20px;
    background: rgba(0,0,0,0.7);
    border-radius: 8px;
    max-width: 80%;
    word-wrap: break-word;
    pointer-events: none;
    z-index: 10;
}

/* Video Controls */
.video-controls {
    display: none;
}

.control-btn {
    background: none;
    border: none;
    color: #e6e6f0;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background 0.2s ease;
}

.control-btn:hover {
    background: rgba(77, 184, 255, 0.2);
}

.time-display {
    font-size: 0.9rem;
    color: #a0a0c0;
    min-width: 100px;
}

.seek-bar, .volume-bar {
    flex: 1;
    height: 6px;
    background: #2a2a3a;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.seek-bar::-webkit-slider-thumb, .volume-bar::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #4db8ff;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 120px;
}

/* Enhanced Timeline */
.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 10px;
}

.timeline-header h3 {
    color: #4db8ff;
    font-size: 1.3rem;
    font-weight: 600;
}

.timeline-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.timeline-container {
    position: relative;
    height: 280px;
    background: #0f0f1a;
    border-radius: 12px;
    border: 1px solid #2a2a4a;
    overflow-x: auto;
    overflow-y: hidden;
    box-shadow: inset 0 2px 8px rgba(0,0,0,0.3);
}

.timeline-ruler {
    height: 40px;
    background: linear-gradient(180deg, #2a2a3a 0%, #1f1f2a 100%);
    border-bottom: 2px solid #3a3a5a;
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 10px;
}

.timeline-track {
    height: 240px;
    position: relative;
    padding: 15px 10px;
    background: linear-gradient(180deg, #1a1a2a 0%, #0f0f1a 100%);
}

.playhead {
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(180deg, #ff4757, #ff3742);
    z-index: 100;
    pointer-events: none;
    border-radius: 2px;
    box-shadow: 0 0 8px rgba(255, 71, 87, 0.6);
}

.playhead::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -6px;
    width: 15px;
    height: 15px;
    background: #ff4757;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.word-block {
    position: absolute;
    background: linear-gradient(135deg, #4db8ff, #3a9fe0);
    color: #0c0c14;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    user-select: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    min-width: 40px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.word-block:hover {
    background: linear-gradient(135deg, #5dc8ff, #4db8ff);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 6px 16px rgba(77, 184, 255, 0.4);
    border-color: rgba(255, 255, 255, 0.3);
    z-index: 50;
}

.word-block.selected {
    background: linear-gradient(135deg, #ff6b6b, #ff5252);
    color: white;
    border-color: #ff4757;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.3), 0 6px 16px rgba(255, 107, 107, 0.4);
    transform: translateY(-2px);
    z-index: 60;
}

.word-block.dragging {
    z-index: 1000;
    transform: rotate(3deg) scale(1.1);
    box-shadow: 0 8px 24px rgba(0,0,0,0.4);
    opacity: 0.9;
}

.word-block.confidence-high {
    border-left: 4px solid #4CAF50;
}

.word-block.confidence-medium {
    border-left: 4px solid #FF9800;
}

.word-block.confidence-low {
    border-left: 4px solid #f44336;
}

/* Timeline time markers */
.time-marker {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 1px;
    background: #4a4a6a;
    pointer-events: none;
}

.time-marker.major {
    background: #6a6a8a;
    width: 2px;
}

.time-marker::after {
    content: attr(data-time);
    position: absolute;
    top: 8px;
    left: 4px;
    font-size: 0.7rem;
    color: #a0a0c0;
    white-space: nowrap;
}

/* Timeline tracks for multiple caption layers */
.caption-track {
    position: relative;
    height: 50px;
    border-bottom: 1px solid #2a2a4a;
    margin-bottom: 10px;
}

.caption-track:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.track-label {
    position: absolute;
    left: -80px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.8rem;
    color: #a0a0c0;
    font-weight: 600;
    width: 70px;
    text-align: right;
}

/* Enhanced timeline controls */
.timeline-zoom-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #1a1a2a;
    padding: 6px 12px;
    border-radius: 6px;
    border: 1px solid #2a2a4a;
}

.zoom-slider {
    width: 80px;
    height: 4px;
    background: #2a2a3a;
    border-radius: 2px;
    outline: none;
    -webkit-appearance: none;
}

.zoom-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4db8ff;
    cursor: pointer;
}

/* Timeline context menu */
.timeline-context-menu {
    background: #1a1a2a;
    border: 1px solid #3a3a5a;
    border-radius: 8px;
    padding: 8px 0;
    box-shadow: 0 8px 24px rgba(0,0,0,0.4);
    z-index: 1000;
    min-width: 180px;
    backdrop-filter: blur(10px);
}

.timeline-context-menu .menu-item {
    padding: 10px 16px;
    cursor: pointer;
    color: #e6e6f0;
    font-size: 0.9rem;
    transition: background 0.2s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.timeline-context-menu .menu-item:hover {
    background: #2a2a3a;
}

.timeline-context-menu .menu-separator {
    height: 1px;
    background: #2a2a4a;
    margin: 4px 0;
}

/* Word editing input */
.word-edit-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #1a1a2a;
    color: #e6e6f0;
    border: 2px solid #4db8ff;
    border-radius: 6px;
    padding: 4px 8px;
    font-size: 0.85rem;
    font-weight: 600;
    z-index: 1001;
    text-align: center;
}

/* Timing preview tooltip */
.timing-preview {
    background: linear-gradient(135deg, #1a1a2a, #2a2a3a);
    color: #4db8ff;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    border: 1px solid #3a3a5a;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    backdrop-filter: blur(10px);
    pointer-events: none;
}

/* Waveform visualization placeholder */
.waveform-container {
    height: 60px;
    background: #0f0f1a;
    border-radius: 6px;
    margin: 10px 0;
    position: relative;
    overflow: hidden;
}

.waveform-bars {
    display: flex;
    align-items: end;
    height: 100%;
    padding: 5px;
    gap: 1px;
}

.waveform-bar {
    background: linear-gradient(180deg, #4db8ff, #3a9fe0);
    width: 2px;
    border-radius: 1px;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.waveform-bar.active {
    opacity: 1;
    background: linear-gradient(180deg, #ff6b6b, #ff5252);
}

/* Control Panels */
.control-panel {
    background: #121220;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #2a2a4a;
}

.control-panel h3 {
    color: #4db8ff;
    font-size: 1.2rem;
    margin-bottom: 15px;
    border-bottom: 1px solid #2a2a4a;
    padding-bottom: 8px;
}

.control-group {
    margin-bottom: 15px;
}

.control-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 0.9rem;
    color: #c0c0e0;
}

.form-control {
    width: 100%;
    padding: 10px;
    background: #1a1a2a;
    border: 1px solid #3a3a5a;
    border-radius: 6px;
    color: #e6e6f0;
    font-size: 0.9rem;
}

.form-control:focus {
    outline: none;
    border-color: #4db8ff;
    box-shadow: 0 0 0 2px rgba(77, 184, 255, 0.2);
}

.form-range {
    width: 100%;
    height: 6px;
    background: #2a2a3a;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.form-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #4db8ff;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}

/* Transcript */
.transcript-container {
    max-height: 300px;
    overflow-y: auto;
}

.transcript-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 20px;
    color: #a0a0c0;
}

.spinner {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(77, 184, 255, 0.3);
    border-top: 3px solid #4db8ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.transcript-content {
    background: #1a1a2a;
    border-radius: 8px;
    padding: 15px;
    max-height: 250px;
    overflow-y: auto;
}

.transcript-word {
    display: inline-block;
    padding: 2px 6px;
    margin: 2px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.transcript-word:hover {
    background: rgba(77, 184, 255, 0.3);
}

.transcript-word.active {
    background: #4db8ff;
    color: #0c0c14;
    font-weight: 600;
}

/* Status Bar */
.status-bar {
    background: #1a1a2e;
    border-top: 1px solid #2a2a4a;
    padding: 12px 30px;
    font-size: 0.9rem;
    color: #a0a0c0;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .editor-section {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto 1fr;
    }

    .video-area {
        grid-column: 1;
        grid-row: 1;
    }

    .caption-controls-area {
        grid-column: 1;
        grid-row: 2;
    }

    .sidebar {
        grid-column: 1;
        grid-row: 3;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        max-height: none;
        overflow-y: visible;
    }

    .timeline-section {
        grid-column: 1;
        grid-row: 4;
    }
}

@media (max-width: 768px) {
    .app-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .header-actions {
        justify-content: center;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .video-controls {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .sidebar {
        grid-template-columns: 1fr;
    }
}

